#!/usr/bin/env python3
"""
RTMonoFlow Evaluation Script
Evaluates RTMonoFlow models with HEEP enhancement against ground truth depth data
Based on evaluate_depth_full.py but adapted for RTMonoFlow architecture
"""

from __future__ import absolute_import, division, print_function

import os
import sys
import cv2
import datasets
import numpy as np
import torch
import time
from layers import disp_to_depth
from torch.utils.data import DataLoader
from tqdm import tqdm
from utils import readlines
import argparse

# Add RTMonoFlow to path
sys.path.append('networks')
sys.path.append('networks/RTMonoDepth')

from networks.RTMonoDepth.RTMonoDepth_s import DepthDecoder, DepthEncoder
from networks.RTMonoDepth.RTMonoFlow import RTMonoFlow, create_rtmonoflow

cv2.setNumThreads(0)  # This speeds up evaluation 5x on our unix systems (OpenCV 3.3.1)

splits_dir = os.path.join(os.path.dirname(__file__), "splits")

# Models which were trained with stereo supervision were trained with a nominal
# baseline of 0.1 units. The KITTI rig has a baseline of 54cm. Therefore,
# to convert our stereo predictions to real-world scale we multiply our depths by 5.4.
STEREO_SCALE_FACTOR = 5.4


def compute_errors(gt, pred):
    """Computation of error metrics between predicted and ground truth depths"""
    thresh = np.maximum((gt / pred), (pred / gt))
    a1 = (thresh < 1.25     ).mean()
    a2 = (thresh < 1.25 ** 2).mean()
    a3 = (thresh < 1.25 ** 3).mean()

    rmse = (gt - pred) ** 2
    rmse = np.sqrt(rmse.mean())

    rmse_log = (np.log(gt) - np.log(pred)) ** 2
    rmse_log = np.sqrt(rmse_log.mean())

    abs_rel = np.mean(np.abs(gt - pred) / gt)
    sq_rel = np.mean(((gt - pred) ** 2) / gt)

    return abs_rel, sq_rel, rmse, rmse_log, a1, a2, a3


def batch_post_process_disparity(l_disp, r_disp):
    """Apply the disparity post-processing method as introduced in Monodepthv1"""
    _, h, w = l_disp.shape
    m_disp = 0.5 * (l_disp + r_disp)
    l, _ = np.meshgrid(np.linspace(0, 1, w), np.linspace(0, 1, h))
    l_mask = (1.0 - np.clip(20 * (l - 0.05), 0, 1))[None, ...]
    r_mask = l_mask[:, :, ::-1]
    return r_mask * l_disp + l_mask * r_disp + (1.0 - l_mask - r_mask) * m_disp


def load_rtmonoflow_model(weights_folder, model_type='adaptive', device='cuda'):
    """Load RTMonoFlow model from weights"""
    
    print(f"-> Loading RTMonoFlow ({model_type}) from {weights_folder}")
    
    # Determine model configuration
    use_adaptive_heep = model_type in ['adaptive', 'adaptive_heep']
    use_flow_blocks = model_type not in ['no_flow', 'no_flow_blocks']
    
    # Create model
    model = create_rtmonoflow(
        use_adaptive_heep=use_adaptive_heep,
        use_flow_blocks=use_flow_blocks
    ).to(device).eval()
    
    # Load weights
    if os.path.exists(os.path.join(weights_folder, "rtmonoflow.pth")):
        # Single file for RTMonoFlow
        model_path = os.path.join(weights_folder, "rtmonoflow.pth")
        model_dict = torch.load(model_path)
        model.load_state_dict(model_dict)
        print(f"-> Loaded RTMonoFlow weights from {model_path}")
    else:
        # Separate encoder/decoder files (fallback)
        encoder_path = os.path.join(weights_folder, "encoder.pth")
        decoder_path = os.path.join(weights_folder, "depth.pth")
        
        if os.path.exists(encoder_path) and os.path.exists(decoder_path):
            encoder_dict = torch.load(encoder_path)
            decoder_dict = torch.load(decoder_path)
            
            # Load encoder weights
            encoder_model_dict = model.encoder.state_dict()
            encoder_weights = {k: v for k, v in encoder_dict.items() 
                             if k in encoder_model_dict and v.shape == encoder_model_dict[k].shape}
            model.encoder.load_state_dict(encoder_weights, strict=False)
            
            # Load decoder weights
            decoder_model_dict = model.decoder.state_dict()
            decoder_weights = {k: v for k, v in decoder_dict.items() 
                             if k in decoder_model_dict and v.shape == decoder_model_dict[k].shape}
            model.decoder.load_state_dict(decoder_weights, strict=False)
            
            print(f"-> Loaded separate encoder/decoder weights")
        else:
            raise FileNotFoundError(f"Could not find model weights in {weights_folder}")
    
    return model


def load_baseline_model(weights_folder, device='cuda'):
    """Load baseline RTMonoDepth model for comparison"""
    
    print(f"-> Loading baseline RTMonoDepth from {weights_folder}")
    
    encoder_path = os.path.join(weights_folder, "encoder.pth")
    decoder_path = os.path.join(weights_folder, "depth.pth")
    
    encoder_dict = torch.load(encoder_path)
    
    encoder = DepthEncoder().to(device).eval()
    depth_decoder = DepthDecoder(encoder.num_ch_enc).to(device).eval()
    
    # Load weights
    model_dict = encoder.state_dict()
    encoder.load_state_dict({k: v for k, v in encoder_dict.items() if
                           (k in model_dict) and encoder_dict[k].shape == model_dict[k].shape})
    depth_decoder.load_state_dict(torch.load(decoder_path))
    
    return encoder, depth_decoder, encoder_dict


def evaluate_model(model, dataloader, opt, model_name="RTMonoFlow"):
    """Evaluate a single model"""
    
    print(f"-> Evaluating {model_name}")
    
    pred_disps = []
    inference_times = []
    
    with torch.no_grad():
        for i, data in enumerate(tqdm(dataloader, desc=f"Evaluating {model_name}")):
            input_color = data[("color", 0, 0)].cuda()
            
            if opt.post_process:
                # Post-processed results require each image to have two forward passes
                input_color = torch.cat((input_color, torch.flip(input_color, [3])), 0)
            
            # Measure inference time
            torch.cuda.synchronize()
            start_time = time.time()
            
            if hasattr(model, 'forward'):
                # RTMonoFlow model
                output = model(input_color)
            else:
                # Baseline model (encoder + decoder)
                encoder, decoder = model
                output = decoder(encoder(input_color))
            
            torch.cuda.synchronize()
            inference_time = time.time() - start_time
            inference_times.append(inference_time)
            
            pred_disp, _ = disp_to_depth(output[("disp", 0)], opt.min_depth, opt.max_depth)
            pred_disp = pred_disp.cpu()[:, 0].numpy()
            
            if opt.post_process:
                N = pred_disp.shape[0] // 2
                pred_disp = batch_post_process_disparity(pred_disp[:N], pred_disp[N:, :, ::-1])
            
            pred_disps.append(pred_disp)
    
    pred_disps = np.concatenate(pred_disps)
    avg_inference_time = np.mean(inference_times)
    fps = 1.0 / avg_inference_time
    
    print(f"-> {model_name} average inference time: {avg_inference_time*1000:.2f} ms ({fps:.1f} FPS)")
    
    return pred_disps, avg_inference_time, fps


def evaluate_depth_accuracy(pred_disps, gt_depths, opt):
    """Evaluate depth accuracy metrics"""
    
    MIN_DEPTH = 1e-3
    MAX_DEPTH = 80
    
    errors = []
    ratios = []
    
    for i in tqdm(range(pred_disps.shape[0]), desc="Computing accuracy metrics"):
        gt_depth = gt_depths[i]
        gt_height, gt_width = gt_depth.shape[:2]
        
        pred_disp = pred_disps[i]
        pred_disp = cv2.resize(pred_disp, (gt_width, gt_height))
        pred_depth = 1 / pred_disp
        
        if opt.eval_split == "eigen":
            mask = np.logical_and(gt_depth > MIN_DEPTH, gt_depth < MAX_DEPTH)
            
            crop = np.array([0.40810811 * gt_height, 0.99189189 * gt_height,
                           0.03594771 * gt_width,  0.96405229 * gt_width]).astype(np.int32)
            crop_mask = np.zeros(mask.shape)
            crop_mask[crop[0]:crop[1], crop[2]:crop[3]] = 1
            mask = np.logical_and(mask, crop_mask)
        else:
            mask = gt_depth > 0
        
        pred_depth = pred_depth[mask]
        gt_depth = gt_depth[mask]
        
        pred_depth *= opt.pred_depth_scale_factor
        if not opt.disable_median_scaling:
            ratio = np.median(gt_depth) / np.median(pred_depth)
            ratios.append(ratio)
            pred_depth *= ratio
        
        pred_depth[pred_depth < MIN_DEPTH] = MIN_DEPTH
        pred_depth[pred_depth > MAX_DEPTH] = MAX_DEPTH
        
        errors.append(compute_errors(gt_depth, pred_depth))
    
    if not opt.disable_median_scaling:
        ratios = np.array(ratios)
        med = np.median(ratios)
        print(" Scaling ratios | med: {:0.3f} | std: {:0.3f}".format(med, np.std(ratios / med)))
    
    mean_errors = np.array(errors).mean(0)
    return mean_errors


def print_results_comparison(results):
    """Print comparison results in a nice format"""
    
    print("\n" + "=" * 100)
    print("RTMONOFLOW EVALUATION RESULTS")
    print("=" * 100)
    
    # Header
    print(f"{'Model':<25} {'abs_rel':<8} {'sq_rel':<8} {'rmse':<8} {'rmse_log':<8} {'a1':<8} {'a2':<8} {'a3':<8} {'FPS':<8}")
    print("-" * 100)
    
    # Print results for each model
    for model_name, result in results.items():
        errors = result['errors']
        fps = result['fps']
        
        print(f"{model_name:<25} {errors[0]:<8.3f} {errors[1]:<8.3f} {errors[2]:<8.3f} "
              f"{errors[3]:<8.3f} {errors[4]:<8.3f} {errors[5]:<8.3f} {errors[6]:<8.3f} {fps:<8.1f}")
    
    # Print improvement analysis
    if 'RTMonoDepth (Baseline)' in results:
        baseline = results['RTMonoDepth (Baseline)']
        
        print("\n" + "=" * 60)
        print("IMPROVEMENT ANALYSIS")
        print("=" * 60)
        
        for model_name, result in results.items():
            if model_name == 'RTMonoDepth (Baseline)':
                continue
            
            errors = result['errors']
            baseline_errors = baseline['errors']
            
            # Calculate improvements (lower is better for most metrics)
            abs_rel_imp = (baseline_errors[0] - errors[0]) / baseline_errors[0] * 100
            rmse_imp = (baseline_errors[2] - errors[2]) / baseline_errors[2] * 100
            a1_imp = (errors[4] - baseline_errors[4]) / baseline_errors[4] * 100
            
            fps_change = (result['fps'] - baseline['fps']) / baseline['fps'] * 100
            
            print(f"\n{model_name}:")
            print(f"  abs_rel improvement: {abs_rel_imp:+.1f}%")
            print(f"  rmse improvement: {rmse_imp:+.1f}%")
            print(f"  a1 improvement: {a1_imp:+.1f}%")
            print(f"  FPS change: {fps_change:+.1f}%")


def main():
    parser = argparse.ArgumentParser(description='Evaluate RTMonoFlow models')
    
    # Model paths
    parser.add_argument('--rtmonoflow_weights', type=str, required=True,
                       help='Path to RTMonoFlow model weights')
    parser.add_argument('--baseline_weights', type=str,
                       help='Path to baseline RTMonoDepth weights for comparison')
    parser.add_argument('--model_type', type=str, default='adaptive',
                       choices=['adaptive', 'standard', 'no_flow'],
                       help='RTMonoFlow model type')
    
    # Data paths
    parser.add_argument('--data_path', type=str, required=True,
                       help='Path to KITTI dataset')
    parser.add_argument('--eval_split', type=str, default='eigen',
                       choices=['eigen', 'eigen_benchmark', 'benchmark'],
                       help='Evaluation split')
    
    # Evaluation options
    parser.add_argument('--eval_mono', action='store_true',
                       help='Evaluate monocular depth estimation')
    parser.add_argument('--eval_stereo', action='store_true',
                       help='Evaluate stereo depth estimation')
    parser.add_argument('--post_process', action='store_true',
                       help='Apply post-processing')
    parser.add_argument('--disable_median_scaling', action='store_true',
                       help='Disable median scaling')
    
    # Technical options
    parser.add_argument('--num_workers', type=int, default=12,
                       help='Number of dataloader workers')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='Batch size for evaluation')
    parser.add_argument('--min_depth', type=float, default=0.1,
                       help='Minimum depth')
    parser.add_argument('--max_depth', type=float, default=100.0,
                       help='Maximum depth')
    parser.add_argument('--pred_depth_scale_factor', type=float, default=1.0,
                       help='Depth scale factor')
    
    # Output options
    parser.add_argument('--save_pred_disps', action='store_true',
                       help='Save predicted disparities')
    parser.add_argument('--output_dir', type=str, default='./evaluation_results',
                       help='Output directory for results')
    
    args = parser.parse_args()
    
    # Validation
    assert sum((args.eval_mono, args.eval_stereo)) == 1, \
        "Please choose mono or stereo evaluation by setting either --eval_mono or --eval_stereo"
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    if args.eval_stereo:
        print("Stereo evaluation - disabling median scaling, scaling by {}".format(STEREO_SCALE_FACTOR))
        args.disable_median_scaling = True
        args.pred_depth_scale_factor = STEREO_SCALE_FACTOR
    else:
        print("Mono evaluation - using median scaling")
    
    # Load dataset
    filenames = readlines(os.path.join(splits_dir, args.eval_split, "test_files.txt"))
    
    # Get image dimensions from baseline model if available
    if args.baseline_weights:
        encoder_path = os.path.join(args.baseline_weights, "encoder.pth")
        encoder_dict = torch.load(encoder_path)
        height, width = encoder_dict['height'], encoder_dict['width']
    else:
        height, width = 192, 640  # Default RTMonoFlow input size
    
    dataset = datasets.KITTIRAWDataset(
        args.data_path, filenames, height, width, [0], 4, 
        is_train=False, use_depth_hints=False
    )
    dataloader = DataLoader(
        dataset, args.batch_size, shuffle=False, 
        num_workers=args.num_workers, pin_memory=True, drop_last=False
    )
    
    print(f"-> Computing predictions with size {width}x{height}")
    print(f"-> Evaluating {len(filenames)} images")
    
    # Load ground truth
    gt_path = os.path.join(splits_dir, args.eval_split, "gt_depths.npz")
    gt_depths = np.load(gt_path, fix_imports=True, encoding='latin1', allow_pickle=True)["data"]
    
    # Evaluate models
    results = {}
    
    # Evaluate RTMonoFlow
    rtmonoflow_model = load_rtmonoflow_model(args.rtmonoflow_weights, args.model_type, device)
    pred_disps, inf_time, fps = evaluate_model(rtmonoflow_model, dataloader, args, f"RTMonoFlow ({args.model_type})")
    errors = evaluate_depth_accuracy(pred_disps, gt_depths, args)
    results[f"RTMonoFlow ({args.model_type})"] = {
        'errors': errors,
        'inference_time': inf_time,
        'fps': fps,
        'pred_disps': pred_disps
    }
    
    # Evaluate baseline if provided
    if args.baseline_weights:
        encoder, decoder, _ = load_baseline_model(args.baseline_weights, device)
        pred_disps_baseline, inf_time_baseline, fps_baseline = evaluate_model(
            (encoder, decoder), dataloader, args, "RTMonoDepth (Baseline)"
        )
        errors_baseline = evaluate_depth_accuracy(pred_disps_baseline, gt_depths, args)
        results["RTMonoDepth (Baseline)"] = {
            'errors': errors_baseline,
            'inference_time': inf_time_baseline,
            'fps': fps_baseline,
            'pred_disps': pred_disps_baseline
        }
    
    # Print results
    print_results_comparison(results)
    
    # Save results if requested
    if args.save_pred_disps:
        os.makedirs(args.output_dir, exist_ok=True)
        for model_name, result in results.items():
            safe_name = model_name.replace(" ", "_").replace("(", "").replace(")", "")
            output_path = os.path.join(args.output_dir, f"disps_{safe_name}_{args.eval_split}.npy")
            np.save(output_path, result['pred_disps'])
            print(f"-> Saved {model_name} predictions to {output_path}")
    
    print("\n-> Evaluation completed!")


if __name__ == "__main__":
    main()
