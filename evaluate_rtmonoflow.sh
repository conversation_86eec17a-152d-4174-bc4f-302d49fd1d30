#!/bin/bash

DEFAULT_MODEL_PATH="./log/rtmonoflow_test/rtmonoflow_quick_test/models/weights_2"
DEFAULT_DATA_PATH="/mnt/acer/kitti_jpg"
DEFAULT_BASELINE_PATH="./log/rtmonoflow_test/rtmonodepth_baseline_quick/models/weights_2"

# Parse command line arguments
MODEL_PATH=${1:-$DEFAULT_MODEL_PATH}
DATA_PATH=${2:-$DEFAULT_DATA_PATH}
BASELINE_PATH=${3:-$DEFAULT_BASELINE_PATH}

echo "Configuration:"
echo "- RTMonoFlow weights: $MODEL_PATH"
echo "- Data path: $DATA_PATH"
echo "- Baseline weights: $BASELINE_PATH"
echo ""

# Check if model weights exist
if [ ! -d "$MODEL_PATH" ]; then
    echo "Warning: RTMonoFlow model weights not found at $MODEL_PATH"
    echo "Available models in log directory:"
    find ./log -name "weights_*" -type d 2>/dev/null | head -10
    echo ""
fi

# Check if data path exists
if [ ! -d "$DATA_PATH" ]; then
    echo "Error: Data path not found at $DATA_PATH"
    echo "Please provide a valid KITTI dataset path"
    exit 1
fi

echo "Starting RTMonoFlow evaluation..."
echo "================================="

# Evaluation 1: RTMonoFlow Adaptive HEEP (Main model)
echo "1. Evaluating RTMonoFlow with Adaptive HEEP"
echo "-------------------------------------------"
python evaluate_flow.py \
    --load_weights_folder "$MODEL_PATH" \
    --model_type adaptive \
    --eval_mono \
    --data_path "$DATA_PATH" \
    --eval_split eigen \
    --baseline_weights "$BASELINE_PATH" \
    --save_pred_disps \
    --output_dir "./evaluation_results/rtmonoflow_adaptive" \
    --num_workers 8 \
    --batch_size 16

echo ""

# Evaluation 2: RTMonoFlow Standard HEEP (if available)
STANDARD_MODEL_PATH="./log/rtmonoflow/rtmonoflow_standard_heep/models/weights_19"
if [ -d "$STANDARD_MODEL_PATH" ]; then
    echo "2. Evaluating RTMonoFlow with Standard HEEP"
    echo "-------------------------------------------"
    python evaluate_flow.py \
        --load_weights_folder "$STANDARD_MODEL_PATH" \
        --model_type standard \
        --eval_mono \
        --data_path "$DATA_PATH" \
        --eval_split eigen \
        --baseline_weights "$BASELINE_PATH" \
        --save_pred_disps \
        --output_dir "./evaluation_results/rtmonoflow_standard" \
        --num_workers 8 \
        --batch_size 16
    echo ""
else
    echo "2. Standard HEEP model not found, skipping..."
    echo ""
fi

# Evaluation 3: Baseline comparison using original evaluation script
echo "3. Evaluating Baseline RTMonoDepth (for comparison)"
echo "---------------------------------------------------"
if [ -d "$BASELINE_PATH" ]; then
    python evaluate_depth_full.py \
        --load_weights_folder "$BASELINE_PATH" \
        --eval_mono \
        --data_path "$DATA_PATH" \
        --eval_split eigen \
        --save_pred_disps \
        --num_workers 8
else
    echo "Baseline model not found at $BASELINE_PATH, skipping..."
fi

echo ""
echo "Evaluation Summary"
echo "=================="
echo "Results saved in:"
echo "- RTMonoFlow Adaptive: ./evaluation_results/rtmonoflow_adaptive/"
echo "- RTMonoFlow Standard: ./evaluation_results/rtmonoflow_standard/"
echo "- Baseline predictions: $BASELINE_PATH/disps_eigen_split.npy"
echo ""

# Optional: Performance comparison
echo "4. Performance Analysis"
echo "----------------------"
echo "Checking evaluation results..."

if [ -f "./evaluation_results/rtmonoflow_adaptive/disps_RTMonoFlow_adaptive_eigen.npy" ]; then
    echo "✓ RTMonoFlow Adaptive evaluation completed"
else
    echo "✗ RTMonoFlow Adaptive evaluation may have failed"
fi

if [ -f "./evaluation_results/rtmonoflow_standard/disps_RTMonoFlow_standard_eigen.npy" ]; then
    echo "✓ RTMonoFlow Standard evaluation completed"
else
    echo "✗ RTMonoFlow Standard evaluation not available"
fi

echo ""
echo "Evaluation completed!"
echo "Check the console output above for detailed metrics comparison."
echo "Disparity predictions have been saved for further analysis."
