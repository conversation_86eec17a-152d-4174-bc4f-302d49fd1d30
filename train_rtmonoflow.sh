#!/bin/bash

# RTMonoFlow Training Script
# Trains the enhanced RTMonoDepth with HEEP from FastFlowNet

echo "Starting RTMonoFlow Training with HEEP Enhancement"
echo "=================================================="

# Set environment variables
export CUDA_VISIBLE_DEVICES=0

# Stage 1: RTMonoFlow with Adaptive HEEP (Recommended)
echo "Stage 1: Training RTMonoFlow with Adaptive HEEP"
python train.py \
--model_name rtmonoflow_adaptive_heep \
--use_rtmonoflow \
--use_adaptive_heep \
--use_flow_blocks \
--heep_pyramid_levels 4 \
--num_epochs 20 \
--learning_rate 1e-4 \
--log_dir ./log/rtmonoflow \
--data_path /mnt/acer/kitti_jpg \
--num_workers 12 \
--batch_size 6 \
--scales 0 1 2 3 \
--height 192 \
--width 640 \
--disparity_smoothness 1e-3 \
--frame_ids 0 -1 1 \
--use_stereo

echo "Stage 1 completed!"

# Stage 2: Fine-tuning with reduced learning rate
echo "Stage 2: Fine-tuning RTMonoFlow"
python train.py \
--model_name rtmonoflow_adaptive_heep_finetune \
--load_weights_folder ./log/rtmonoflow/rtmonoflow_adaptive_heep/models/weights_19 \
--use_rtmonoflow \
--use_adaptive_heep \
--use_flow_blocks \
--heep_pyramid_levels 4 \
--num_epochs 10 \
--learning_rate 5e-5 \
--log_dir ./log/rtmonoflow \
--data_path /mnt/acer/kitti_jpg \
--num_workers 12 \
--batch_size 8 \
--scales 0 1 2 3 \
--height 192 \
--width 640 \
--disparity_smoothness 1e-3 \
--frame_ids 0 -1 1 \
--use_stereo

echo "Stage 2 completed!"

# Stage 3: RTMonoFlow with Standard HEEP (Alternative)
echo "Stage 3: Training RTMonoFlow with Standard HEEP (Alternative)"
python train.py \
--model_name rtmonoflow_standard_heep \
--use_rtmonoflow \
--use_flow_blocks \
--heep_pyramid_levels 4 \
--num_epochs 20 \
--learning_rate 1e-4 \
--log_dir ./log/rtmonoflow \
--data_path /mnt/acer/kitti_jpg \
--num_workers 12 \
--batch_size 8 \
--scales 0 1 2 3 \
--height 192 \
--width 640 \
--disparity_smoothness 1e-3 \
--frame_ids 0 -1 1 \
--use_stereo
